import asyncio
import re
from typing import Any, AsyncIterator, Dict

from agent_server.core.base.langchain_sender import LangchainBaseSender
from agent_server.core.config.app_constant import AppConstant
from agent_server.core.rag.utils import get_rag_service

from .messages.markdown import create_markdown_message_chunks
from .messages.choices import RADIO_MESSAGE, radio_choices_message_chunks, select_message_chunks
from .messages.mermaid import create_mermaid_message_chunks
from .messages.thinking import create_thinking_message_chunks
from .messages.tool import create_tool_message_chunks
from .messages.error import create_error_message_chunks
from .messages.state import create_message_state_message_chunks
from .messages.thought_chain import thought_chain_event_generator
from .messages.form import create_form_message_chunks
from .messages.test import create_test_message_chunks
from .messages.widget import create_widget_message_chunks

chunks = {
    "markdown": create_markdown_message_chunks(),
    "radio": radio_choices_message_chunks,
    "select": select_message_chunks,
    "mermaid": create_mermaid_message_chunks(),
    "thinking": create_thinking_message_chunks(),
    "tool": create_tool_message_chunks(),
    "error": create_error_message_chunks(),
    "state": create_message_state_message_chunks(),
    "thought_chain": None,
    "form": create_form_message_chunks(),
    "test": create_test_message_chunks(),
    "widget": create_widget_message_chunks(),
}


async def default_event_generator(message_type: str = "radio_choices"):
    mock_message_chunks = chunks[message_type]
    await asyncio.sleep(0.1)

    for message_chunk in mock_message_chunks:
        if message_chunk:
            if message_chunk.get("is_network_error") is True:
                return
            if message_chunk.get("is_error") is True:
                yield message_chunk
                continue
            # Convert the chunk to JSON and format as SSE
            yield message_chunk
        await asyncio.sleep(0.1)

def get_message_type(user_message: str):
    msg = user_message.lower().strip()

    msg = re.sub(r"<message-embedded>[\w\W]+</message-embedded>", "", msg)
    types = chunks.keys()
    for msg_type in types:
        if msg.startswith(msg_type):
            return msg_type
    return ""

class TestAgent(LangchainBaseSender):

    async def _build_final_question(self, raw_question: str) -> str:
        rag_service = get_rag_service("ragflow")
        
        doc_content = ""
        reference = ""
        if self.files:
            doc_chunks = await rag_service.retrieve_sync_db(self.question, self.files, self.user_id)
            doc_content = "\n\n".join([i["content"] for i in doc_chunks])
            reference = f"""
            # 只能使用以下参考文档内容回复用户问题：
            {doc_content}
            """

        return f"""
        # 用户消息：
        {raw_question}

        -----------------

        {reference}

        -----------------

        # 注意：
        无法理解用户消息内容时，请输出以下默认消息：
        模型未理解消息内容返回为空，请选择消息类型：{RADIO_MESSAGE}
        
        """

    def _handle_stream_content(self, chunk: Dict[str, Any]) -> dict:
        _package_type = chunk.get("package_type", AppConstant.DEFAULT_PACKAGE_TYPE)
        return self._wrap_package(chunk.get("content"), _package_type)
    
    async def _handle_response(self, chunk: Dict[str, Any]) -> AsyncIterator[Any]:
        message_type = get_message_type(self.question)

        if not message_type:
            yield " "
        else:
            generator_fn = (
                thought_chain_event_generator
                if message_type == "thought_chain"
                else default_event_generator
            )
            async for message in generator_fn(message_type):
                yield message
