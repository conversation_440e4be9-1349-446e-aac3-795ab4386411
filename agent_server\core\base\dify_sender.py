from typing import Dict, Any, List, Optional, AsyncIterator
from fastapi import Request
from agent_server.llmclient.dify_client import DifyClient
from agent_server.core.config.app_logger import logger
from agent_server.utils.common_utils import stream_with_last_
from agent_server.core.base.base_sender import BaseSender
import json


# Dify 发送器基类，用于封装流式响应生成逻辑。
# <AUTHOR> xiangjh
class DifyBaseSender(BaseSender):
    """
    Dify 平台消息发送器基类，继承自 BaseSender。

    提供对 Dify 客户端流式响应的封装支持，包含：
    - 会话 ID 生成
    - 问题构建（可重写）
    - chunk 数据解析与转换
    - 异步流式数据块生成
    - 错误处理与日志记录

    Attributes:
        agent_code (str): 当前 Agent 的唯一标识符，用于会话 ID 生成及日志上下文识别。
    """

    def __init__(self, agent_code: str):
        """
        初始化 DifyBaseSender 实例。

        Args:
            agent_code (str): 当前 Agent 的唯一标识符，用于：
                - 生成会话 ID（session_id）
                - 日志记录上下文识别
                - 区分不同 Agent 的行为逻辑（可选）
        """
        super().__init__(agent_code)

    async def _generate_content(
        self,
        request: Request,
        question: str,
        user_id: str,
        conversation_id: str,
        extend_params: dict = None,
        files: List[str] = []
    ) -> AsyncIterator[Any]:
        """
        从 Dify 客户端获取异步流并逐个处理和产出消息内容。
        每个产出项应为一个字典对象，格式如下：
            {
                "data": str | Any,       # 需要发送的数据内容
                "package_type": int,     # 消息类型标识（如：0 表示普通文本）
                "is_last": bool          # 是否为最后一个数据块
            }

        Args:
            request (Request): FastAPI 请求对象。
            question (str): 用户输入的问题文本。
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。

        Yields:
            AsyncIterator[Any]: 异步生成的消息内容片段，包含 data、is_last、package_type 等字段。

        Raises:
            Exception: 在调用 Dify 流过程中若发生异常，将被捕获并记录日志，同时向客户端发送错误信息。
        """
        try:
            dify_client = DifyClient(user_id=user_id, agent_id=self.agent_code)
            final_question = self._build_final_question(question)
            logger.info(f"原始问题：{final_question}")
            session_id = self._generate_session_id(user_id, conversation_id)

            async for processed_data, is_last in stream_with_last_(
                self._process_dify_stream(dify_client, final_question, session_id)
            ):
                logger.debug(
                    f"[SENDING] 发送 chunk: {processed_data}..., is_last={is_last}"
                )
                yield {
                    "data": processed_data,
                    "is_last": is_last,
                    "package_type": 0,
                    "is_new_package": False,
                }

        except Exception as e:
            error_msg = f"内部服务异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return

    async def _process_dify_stream(self, client, question, session_id):
        """
        处理 Dify 原始流输出，逐个解析 chunk 并进行格式转换。

        Args:
            client (DifyClient): 已初始化的 Dify 客户端实例。
            question (str): 构建后的最终问题字符串。
            session_id (str): 当前会话 ID。

        Yields:
            str: 经 process_chunk() 处理后的字符串形式的消息内容。
        """
        async for chunk in client.chat_stream(question=question, session_id=session_id):
            processed = process_chunk(json.loads(chunk))
            if processed:
                yield processed

    def _build_final_question(self, raw_question: str) -> str:
        """
        构建最终发送给 Dify 的问题内容。

        子类可重写此方法以实现自定义问题构造逻辑，
        默认情况下直接返回原始问题。

        Args:
            raw_question (str): 原始用户问题。

        Returns:
            str: 构造后的最终问题。
        """
        logger.info(f"原始问题：{raw_question}")
        return raw_question


# 保留原有工具方法（可考虑后续迁移到类中）
def process_chunk(chunk: Dict[str, Any]) -> Optional[str]:
    """
    处理 chunk 数据，根据类型返回对应的内容。

    支持：
        - type == 'stream': 返回 content 文本
        - type == 'tool_start': 返回 XML 格式的工具调用字符串
        - type == 'tool_result': {"type": "tool_result", "tool": "check_page_config", "output": "[TextContent(type='text', text='{\"success\": true, \"config_json\": {\"dynamicSql\": \"select did,name,type_name,code,dlevel,price,delete_flag from system_page_demo\", \"tableParams\": {\"fieldResizableTitle\": true}}, \"resultMessage\": \"配置验证通过，请继续调用 save_page_config
                    工具进行保存\"}', annotations=None)]"}
        - type == 'error': 返回 message 文本
    """
    chunk_type = chunk.get("type")

    if chunk_type == "stream":
        return chunk.get("content", "")

    elif chunk_type == "tool_result":
        tool_name = chunk.get("tool")
        if not tool_name:
            return None

        tool_output = chunk.get("output", [])
        success = False
        status = "success"
        if tool_output and isinstance(tool_output, list) and len(tool_output) > 0:
            text_content = tool_output[0]
            if hasattr(text_content, "text") and text_content.text:
                try:
                    content_data = json.loads(text_content.text)
                    success = content_data.get("success", False)
                    status = "success" if success else "failed"
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode TextContent: {e}")

        # 构建XML数据，包含success信息
        xml_data = f"""<message-embedded>
                            <widget>
                                <code>@BuildIn/Tool</code>
                                <props>
                                    <name>{tool_name}</name>
                                    <status>{status}</status>
                                </props>
                            </widget>
                        </message-embedded>"""
        return xml_data
    elif chunk_type == "error":
        return chunk.get("message", "")
    else:
        # 其他类型暂不处理
        return None
