"""add files field to message

Revision ID: f1841b297ff2
Revises: dfa19b24e09a
Create Date: 2025-09-19 09:53:27.192470

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f1841b297ff2'
down_revision: Union[str, None] = 'dfa19b24e09a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('message', sa.Column('files', sa.ARRAY(sa.String()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('message', 'files')
    # ### end Alembic commands ###
