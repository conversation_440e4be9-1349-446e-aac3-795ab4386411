
[project]
name = "agent-server"
dynamic = [
    "version",
    "description",
    "readme",
    "authors",
    "keywords",
    "urls",
    "scripts",
]
license = "MIT"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

requires-python = ">=3.11, <3.13"
dependencies = [
    "alembic>=1.15.2",
    "asyncpg>=0.30.0",
    "dotenv>=0.9.9",
    "fastapi[standard]>=0.115.11",
    "httpx>=0.28.1",
    "langchain-community>=0.3.24",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.5.0",
    "langgraph-checkpoint-redis>=0.1.0",
    "loguru>=0.7.3",
    "mcp==1.9.0",
    "pillow>=11.1.0",
    "psycopg2-binary>=2.9.10",
    "psycopg[binary,pool]>=3.2.6",
    "pylint>=3.3.6",
    "redis>=6.1.0",
    "sqlalchemy>=2.0.41",
    "sqlmodel>=0.0.24",
    "pyyaml>=6.0",
    "nicegui>=1.4.0",
    "bcrypt>=4.0.0",
    "numpy>=1.24.0",
    "aiohttp>=3.8.0",
    "setuptools>=80.9.0",
    "xxhash>=3.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]
test = ["pytest>=7.4.0", "pytest-asyncio>=0.21.0", "pytest-cov>=4.1.0"]
rags = ["psutil>=5.9.0", "pymilvus>=2.3.0"]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true
