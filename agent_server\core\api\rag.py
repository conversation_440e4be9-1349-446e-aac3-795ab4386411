"""
RAG (检索增强生成) API 模块

此模块为 RAG 功能提供全面的 API 端点，包括：
- 文档上传和管理
- 文件处理状态跟踪
- 语义文档检索
- 基于 RAG 的响应生成

该 API 与 RagFlowRAGService 集成，提供：
- 多格式文档支持（PDF、TXT、DOCX 等）
- 自动文档解析和索引
- 基于向量的语义搜索
- 具有适当访问控制的用户范围文件管理

所有端点都需要身份验证，并实现适当的错误处理、
输入验证和全面的日志记录。

作者：AI Assistant
日期：2025-09-12
"""

from typing import List, Optional, Dict, Any
from agent_server.core.rag.utils import get_rag_service
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    UploadFile,
    File,
    Query,
    Path,
    Body,
)
from pydantic import BaseModel, Field
from datetime import datetime

from agent_server.core.vo.user_vo import UserVO
from agent_server.core.auth.security import check_user
from agent_server.core.api.response import StandardResponse, Pagination
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.user_uploaded_files import user_uploaded_files_crud
from agent_server.core.services.database.schemas.user_uploaded_files import (
    UserUploadedFilesRead,
    FileStatus,
)
from agent_server.core.rag.ragflow import (
    RagFlowError,
    RagFlowAuthenticationError,
    RagFlowNetworkError,
    RagFlowAPIError,
)
from agent_server.core.config.app_logger import logger

router = APIRouter(prefix="/rag", tags=["RAG"])

# Request/Response Models


class FileUploadResponse(BaseModel):
    """File upload response model"""
    id: str = Field(description="Unique file identifier")
    rag_file_id: str = Field(description="RAG service file identifier")
    file_name: str = Field(description="Original file name")
    file_size: int = Field(description="File size in bytes")
    status: FileStatus = Field(description="Current processing status")
    message: str = Field(description="Upload result message")


class FileStatusResponse(BaseModel):
    """File status response model"""

    file_id: str = Field(description="File identifier")
    status: FileStatus = Field(description="Current processing status")
    progress: Optional[float] = Field(None, description="Processing progress (0-1)")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    updated_at: datetime = Field(description="Last update timestamp")


class FileListResponse(BaseModel):
    """File list response model"""

    files: List[UserUploadedFilesRead] = Field(description="List of user files")
    total: int = Field(description="Total number of files")
    page: int = Field(description="Current page number")
    page_size: int = Field(description="Number of files per page")


class DocumentRetrieveRequest(BaseModel):
    """Document retrieval request model"""

    query: str = Field(description="Search query text")
    file_ids: Optional[List[str]] = Field(
        None, description="Specific user uploaded file IDs to search in"
    )
    limit: int = Field(default=10, ge=1, le=50, description="Maximum number of results")
    similarity_threshold: float = Field(
        default=0.1, ge=0.0, le=1.0, description="Minimum similarity score"
    )


class DocumentChunk(BaseModel):
    """Document chunk model"""

    content: str = Field(description="Document chunk content")
    score: float = Field(description="Similarity score")
    document_id: str = Field(description="Source document ID")
    chunk_id: str = Field(description="Chunk identifier")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class DocumentRetrieveResponse(BaseModel):
    """Document retrieval response model"""

    query: str = Field(description="Original search query")
    results: List[DocumentChunk] = Field(description="Retrieved document chunks")
    total_results: int = Field(description="Total number of results found")


class RAGGenerateRequest(BaseModel):
    """RAG generation request model"""

    query: str = Field(description="User query for RAG generation")
    file_ids: Optional[List[str]] = Field(
        None, description="Specific user uploaded file IDs to use for context"
    )
    max_context_length: int = Field(
        default=4000, ge=500, le=8000, description="Maximum context length"
    )
    temperature: float = Field(
        default=0.7, ge=0.0, le=2.0, description="Generation temperature"
    )
    include_sources: bool = Field(
        default=True, description="Whether to include source references"
    )


class RAGGenerateResponse(BaseModel):
    """RAG generation response model"""

    query: str = Field(description="Original user query")
    response: str = Field(description="Generated response")
    sources: Optional[List[DocumentChunk]] = Field(
        None, description="Source documents used"
    )
    context_used: int = Field(description="Number of context tokens used")


class FileBatchDeleteRequest(BaseModel):
    """Batch file deletion request model"""

    file_ids: List[str] = Field(description="List of user uploaded file IDs to delete")


class FileBatchDeleteResponse(BaseModel):
    """Batch file deletion response model"""

    deleted_files: List[str] = Field(description="Successfully deleted file IDs")
    failed_files: List[Dict[str, str]] = Field(
        description="Failed deletions with error messages"
    )
    total_deleted: int = Field(description="Total number of files deleted")
    total_failed: int = Field(description="Total number of failed deletions")



# 文件管理端点


@router.post("/files/upload", response_model=StandardResponse[FileUploadResponse])
async def upload_file(
    file: UploadFile = File(..., description="要上传进行 RAG 处理的文件"),
    user: UserVO = Depends(check_user),
):
    """
    上传文档进行 RAG 处理

    此端点接受文件上传并通过 RAG 服务处理它们。
    文件将被上传到 RagFlow，解析并索引以供后续检索。

    Args:
        file: 上传的文件（支持 PDF、TXT、DOCX 等）
        user: 当前认证用户

    Returns:
        包含文件上传详情和处理状态的 StandardResponse

    Raises:
        HTTPException: 如果文件上传失败或文件类型不受支持
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名是必需的")

        # 检查文件大小（限制为 50MB）
        MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="文件大小超过 50MB 限制")

        # 初始化 RAG 服务
        rag_service = get_rag_service()

        # 上传文件到 RAG 服务
        logger.info(f"用户 {user.userId} 正在上传文件: {file.filename}")
        result = await rag_service.upload_file(
            user_id=str(user.userId), file_content=file_content, file_name=file.filename
        )

        if not result.success:
            raise HTTPException(
                status_code=500, detail="上传文件到 RAG 服务失败"
            )

        # 异步触发解析，如果文件已经存在跳过解析
        # TODO 判断文件状态是否解析
        if not result.existed:
            try:
                await rag_service.parse_file(result.rag_file_id)
                logger.info(f"文件解析已触发: {result.rag_file_id}")
            except Exception as e:
                logger.warning(f"触发解析失败 {result.rag_file_id}: {e}")

        # 准备响应
        response_data = FileUploadResponse(
            id=result.id,
            rag_file_id=result.rag_file_id,
            file_name=file.filename,
            file_size=len(file_content),
            status=result.status,
            message="文件上传成功并已启动解析",
        )

        return StandardResponse(
            data=response_data, code=200, message="文件上传成功"
        )

    except RagFlowAuthenticationError:
        logger.error("RAG 服务认证失败")
        raise HTTPException(status_code=401, detail="RAG 服务认证失败")
    except RagFlowNetworkError:
        logger.error("RAG 服务网络错误")
        raise HTTPException(
            status_code=503, detail="RAG 服务暂时不可用"
        )
    except RagFlowAPIError as e:
        logger.error(f"RAG 服务 API 错误: {e}")
        raise HTTPException(status_code=500, detail=f"RAG 服务错误: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传过程中发生意外错误: {e}")
        raise HTTPException(
            status_code=500, detail="文件上传过程中发生内部服务器错误"
        )


@router.get("/files", response_model=StandardResponse[FileListResponse])
async def list_user_files(
    skip: int = Query(0, ge=0, description="要跳过的文件数"),
    limit: int = Query(20, ge=1, le=100, description="要返回的文件数"),
    status: Optional[FileStatus] = Query(None, description="按文件状态过滤"),
    user: UserVO = Depends(check_user),
):
    """
    列出用户上传的文件，支持分页和过滤

    Args:
        skip: 分页时要跳过的文件数
        limit: 要返回的最大文件数
        status: 可选的状态过滤器
        user: 当前认证用户

    Returns:
        包含分页用户文件列表的 StandardResponse
    """
    try:
        async with db_manager.session() as session:
            # 获取用户文件（分页）
            files = await user_uploaded_files_crud.get_by_user_id(
                session, user_id=user.userId, skip=skip, limit=limit, status=status
            )

            # 获取分页的总数
            total = await user_uploaded_files_crud.count_by_user_id(
                session, user_id=user.userId, status=status
            )

            response_data = FileListResponse(
                files=files, total=total, page=skip // limit + 1, page_size=limit
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="文件检索成功",
                pagination=Pagination(
                    page=skip // limit + 1, pageSize=limit, total=total
                ),
            )

    except Exception as e:
        logger.error(f"检索用户文件时出错: {e}")
        raise HTTPException(status_code=500, detail="检索文件失败")


@router.get("/files/{file_id}", response_model=StandardResponse[UserUploadedFilesRead])
async def get_file_details(
    file_id: str = Path(..., description="文件标识符"),
    user: UserVO = Depends(check_user),
):
    """
    获取特定文件的详细信息

    Args:
        file_id: 文件标识符
        user: 当前认证用户

    Returns:
        包含文件详情的 StandardResponse

    Raises:
        HTTPException: 如果文件未找到或访问被拒绝
    """
    try:
        async with db_manager.session() as session:
            # 通过 rag_file_id 获取文件并验证所有权
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=file_id
            )

            if not file_record:
                raise HTTPException(status_code=404, detail="文件未找到")

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="访问被拒绝")

            return StandardResponse(
                data=file_record,
                code=200,
                message="文件详情检索成功",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检索文件详情时出错: {e}")
        raise HTTPException(status_code=500, detail="检索文件详情失败")


@router.get(
    "/files/{file_id}/status", response_model=StandardResponse[FileStatusResponse]
)
async def get_file_status(
    file_id: str = Path(..., description="文件标识符"),
    user: UserVO = Depends(check_user),
):
    """
    检查特定文件的处理状态

    Args:
        file_id: 文件标识符
        user: 当前认证用户

    Returns:
        包含文件处理状态的 StandardResponse

    Raises:
        HTTPException: 如果文件未找到或访问被拒绝
    """
    try:
        async with db_manager.session() as session:
            file = await user_uploaded_files_crud.get(session, id=file_id)

            if not file:
                raise HTTPException(status_code=404, detail="文件未找到")

            rag_file_id = file.rag_file_id

            # 验证文件所有权
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=rag_file_id
            )

            if not file_record:
                raise HTTPException(
                    status_code=404, detail="从 RAG 服务中未找到文件"
                )

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="访问被拒绝")

        # 从 RAG 服务获取实时状态
        rag_service = get_rag_service()
        try:
            status_info = await rag_service.get_file_status(file_id)

            response_data = FileStatusResponse(
                file_id=file_id,
                status=status_info.get("status", file_record.status),
                progress=status_info.get("progress"),
                error_message=status_info.get(
                    "error_message", file_record.error_message
                ),
                updated_at=file_record.updated_at,
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="文件状态检索成功",
            )

        except RagFlowError as e:
            logger.warning(f"从 RAG 服务获取实时状态失败: {e}")
            # 回退到数据库状态
            response_data = FileStatusResponse(
                file_id=file_id,
                status=file_record.status,
                progress=None,
                error_message=file_record.error_message,
                updated_at=file_record.updated_at,
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="从数据库检索文件状态（RAG 服务不可用）",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检索文件状态时出错: {e}")
        raise HTTPException(status_code=500, detail="检索文件状态失败")


@router.post("/files/{file_id}/parse", response_model=StandardResponse[Dict[str, str]])
async def trigger_file_parsing(
    file_id: str = Path(..., description="文件标识符"),
    user: UserVO = Depends(check_user),
):
    """
    手动触发特定文件的解析

    此端点可用于重试解析失败的文件，
    或触发已上传但未解析的文件的解析。

    Args:
        file_id: 文件标识符
        user: 当前认证用户

    Returns:
        指示解析触发结果的 StandardResponse

    Raises:
        HTTPException: 如果文件未找到、访问被拒绝或解析失败
    """
    try:
        async with db_manager.session() as session:
            # 验证文件所有权
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=file_id
            )

            if not file_record:
                raise HTTPException(status_code=404, detail="文件未找到")

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="访问被拒绝")

        # 通过 RAG 服务触发解析
        rag_service = get_rag_service()
        success = await rag_service.parse_file(file_id)

        if success:
            return StandardResponse(
                data={"file_id": file_id, "status": "parsing_triggered"},
                code=200,
                message="文件解析触发成功",
            )
        else:
            raise HTTPException(
                status_code=500, detail="触发文件解析失败"
            )

    except RagFlowAuthenticationError:
        raise HTTPException(status_code=401, detail="RAG 服务认证失败")
    except RagFlowNetworkError:
        raise HTTPException(
            status_code=503, detail="RAG 服务暂时不可用"
        )
    except RagFlowAPIError as e:
        raise HTTPException(status_code=500, detail=f"RAG 服务错误: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"触发文件解析时出错: {e}")
        raise HTTPException(status_code=500, detail="触发文件解析失败")


@router.delete("/files", response_model=StandardResponse[FileBatchDeleteResponse])
async def delete_files(
    request: FileBatchDeleteRequest = Body(..., description="文件删除请求"),
    user: UserVO = Depends(check_user),
):
    """
    从 RAG 服务和数据库中删除多个文件

    此端点执行文件的批量删除。它将首先尝试从 RAG 服务中删除文件，
    然后从数据库中删除记录。

    file_ids 参数现在接受来自 user_uploaded_files 表的面向用户的文件 ID，
    而不是内部 RAG 文件 ID。

    Args:
        request: 包含用户上传文件 ID 的批量删除请求
        user: 当前认证用户

    Returns:
        包含删除结果的 StandardResponse
    """
    try:
        # 获取 RAG 服务实例
        rag_service = get_rag_service()

        # 使用基类的 delete_files 方法处理删除逻辑
        result = await rag_service.delete_files(
            ids=request.file_ids,
            user_id=str(user.userId)
        )

        # 将 FileDeleteResult 转换为 API 响应格式
        response_data = FileBatchDeleteResponse(
            deleted_files=result.deleted_files,
            failed_files=result.failed_files,
            total_deleted=result.total_deleted,
            total_failed=result.total_failed,
        )

        return StandardResponse(
            data=response_data,
            code=200,
            message=f"删除完成: {result.total_deleted} 成功, {result.total_failed} 失败",
        )

    except Exception as e:
        logger.error(f"批量文件删除过程中出错: {e}")
        raise HTTPException(status_code=500, detail="删除文件失败")


# 文档检索端点


@router.post("/retrieve", response_model=StandardResponse[DocumentRetrieveResponse])
async def retrieve_documents(
    request: DocumentRetrieveRequest = Body(
        ..., description="文档检索请求"
    ),
    user: UserVO = Depends(check_user),
):
    """
    基于语义相似性搜索检索相关文档

    此端点搜索用户上传的文档，以查找与提供的查询
    语义相似的内容。

    file_ids 参数现在接受来自 user_uploaded_files 表的面向用户的文件 ID，
    而不是内部 RAG 文件 ID。

    Args:
        request: 包含查询和参数的文档检索请求
        user: 当前认证用户

    Returns:
        包含检索到的文档块的 StandardResponse

    Raises:
        HTTPException: 如果检索失败或未找到文档
    """
    file_ids = request.file_ids
    limit = request.limit

    try:
        if not file_ids:
            return StandardResponse(
                data=DocumentRetrieveResponse(
                    query=request.query, results=[], total_results=0
                ),
                code=200,
                message="没有可用于搜索的文档",
            )

        # 通过 RAG 服务执行检索
        rag_service = get_rag_service()
        results = await rag_service.retrieve(
            query=request.query,
            ids=file_ids,
            user_id=str(user.userId),
        )

        # 将结果转换为响应格式
        document_chunks = []
        for result in results[: limit]:
            if result.get("similarity", 0) >= request.similarity_threshold:
                chunk = DocumentChunk(
                    content=result.get("content", ""),
                    score=result.get("similarity", 0.0),
                    document_id=result.get("document_id", ""),
                    chunk_id=result.get("id", ""),
                    metadata=result.get("metadata", {}),
                )
                document_chunks.append(chunk)

        response_data = DocumentRetrieveResponse(
            query=request.query,
            results=document_chunks,
            total_results=len(document_chunks),
        )

        return StandardResponse(
            data=response_data,
            code=200,
            message=f"检索到 {len(document_chunks)} 个相关文档块",
        )

    except RagFlowAuthenticationError:
        raise HTTPException(status_code=401, detail="RAG 服务认证失败")
    except RagFlowNetworkError:
        raise HTTPException(
            status_code=503, detail="RAG 服务暂时不可用"
        )
    except RagFlowAPIError as e:
        raise HTTPException(status_code=500, detail=f"RAG 服务错误: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档检索过程中出错: {e}")
        raise HTTPException(status_code=500, detail="检索文档失败")


# RAG 生成端点


@router.post("/search", response_model=StandardResponse[DocumentRetrieveResponse])
async def search_documents(
    query: str = Body(..., description="搜索查询"),
    file_ids: Optional[List[str]] = Body(
        None, description="要搜索的特定用户上传文件 ID"
    ),
    limit: int = Body(10, ge=1, le=50, description="最大结果数"),
    similarity_threshold: float = Body(
        0.1, ge=0.0, le=1.0, description="最小相似度分数"
    ),
    user: UserVO = Depends(check_user),
):
    """
    具有过滤选项的高级文档搜索

    这是 /retrieve 的替代端点，具有更简单的请求格式。
    file_ids 参数接受来自 user_uploaded_files 表的面向用户的文件 ID。

    Args:
        query: 搜索查询文本
        file_ids: 要搜索的特定用户上传文件 ID 的可选列表
        limit: 要返回的最大结果数
        similarity_threshold: 结果的最小相似度分数
        user: 当前认证用户

    Returns:
        包含搜索结果的 StandardResponse
    """
    # 使用构造的请求重用 retrieve_documents 逻辑
    request = DocumentRetrieveRequest(
        query=query,
        file_ids=file_ids,
        limit=limit,
        similarity_threshold=similarity_threshold,
    )

    return await retrieve_documents(request, user)
