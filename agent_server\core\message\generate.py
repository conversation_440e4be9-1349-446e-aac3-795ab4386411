from enum import Enum
import asyncio
from typing import Any, Dict, List
from uuid import uuid4
from datetime import datetime, timezone

from agent_server.utils.redis_util import redis_client
from agent_server.core.message.transmitter import Transmitter
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationTable,
)
from agent_server.core.message.types import MessageType, MessagePackage
from agent_server.core.services.database.schemas.message import MessageCreate
from agent_server.core.services.database.crud.message import message_curd
from agent_server.core.config.app_config import config


CONVERSATION_TITLE_MAX_LENGTH = 20
START_DELAY: float = 0.5
MESSAGE_CONTENT_OFFSET: int = 15
class MessageSignal(str, Enum):
    Error = "[[<<ERROR>>]]"
    Start = "[[<<START>>]]"
    Done = "[[<<DONE>>]]"
    Cancel = "[[<<CANCEL>>]]"


def create_error_message(error: str):
    return f"{MessageSignal}: {error}"


def create_start_message():
    return MessageSignal.Start


def create_done_message():
    return MessageSignal.Done


def create_cancel_message(reason: str):
    return f"{MessageSignal.Cancel}: {reason}"


def check_existed(key: str):
    ttl = redis_client.client.ttl(key)
    # ttl == -2 -> key 不存在 (极少数 race 情况)， ttl == -1 -> 无过期
    if ttl in (-1, -2):
        return False
    return True


def get_latest_chunk(key: str):
    redis_client.client.lindex(key, 0)


def push_message_chunk(key: str, message: str):
    redis_client.client.lpush(key, message)


def get_all_chunks(key: str):
    redis_client.client.lrange(key, 0, -1)


def get_message_content(message: str):
    return message[MESSAGE_CONTENT_OFFSET:]


def getPreservedKey(key: str):
    return f"{key}_preserved"


async def message_generate(task_id: str, message_generator: Any):
    try:
        async for msg in message_generator:
            # 收到 cancel 信号中断生成
            head = get_latest_chunk(task_id)
            if head and head.startswith(MessageSignal.Cancel):
                return
            push_message_chunk(key=task_id, message=msg)
    except Exception as e:
        print(f"Error: {e}")
        push_message_chunk(key=task_id, message=create_error_message(str(e) + "\n"))
    finally:
        push_message_chunk(key=task_id, message=create_done_message())


async def run_generate_message_task(
    task_id: str,
    conversation_id: str,
    agent_code: str,
    message_generator_fn: Any,
):
    """Run task in thread with synchronous database operations"""

    # 插入占位符，保证列表永远不为空
    push_message_chunk(key=task_id, message=create_start_message())

    async def task():
        try:
            transmitter = Transmitter(
                conversation_id=conversation_id,
                message_id=uuid4().hex,
                agent_code=agent_code,
            )

            await message_generate(
                task_id,
                message_generator_fn(transmitter),
            )

        except Exception as e:
            print(f"Task error: {e}")
            push_message_chunk(key=task_id, message=create_error_message(e))

    # 后台运行 task
    generate_task = asyncio.create_task(task())
    asyncio.shield(generate_task)


def subscribe_message(task_id):
    result = redis_client.client.brpop(task_id, config.message.waiting_timeout)

    if result and result[1]:
        # 将生成的消息数据存在 key=task_id_preserved 中缓存
        # key=task_id 的值中只保存最新的数据，消息块消费后即被删除
        preserved_data_key = getPreservedKey(task_id)
        push_message_chunk(key=preserved_data_key, message=result[1])
        return result[1]
    else:
        return None


class MessageStreamHandler:
    """消息流处理器，负责管理消息的生成和分发"""

    def __init__(self, task_id: str, conversation_id: str):
        self.task_id = task_id
        self.conversation_id = conversation_id
        self.preserved_data_key = getPreservedKey(task_id)

    async def start(self):
        asyncio.sleep(START_DELAY)
        await self.get_cached_messages()
        await self.stream_new_messages()

    async def get_cached_messages(self):
        """获取已缓存的消息"""
        all_created_messages = get_all_chunks(
            self.preserved_data_key
        )
        if all_created_messages:
            all_created_messages.reverse()
            for message in all_created_messages:
                if self._should_skip_message(message):
                    continue
                if self._is_terminal_message(message):
                    return
                yield message

    def _should_skip_message(self, message: str) -> bool:
        """判断是否应该跳过消息"""
        return message == MessageSignal.Start

    def _is_terminal_message(self, message: str) -> bool:
        """判断是否为终止消息"""
        return message == MessageSignal.Done or message.startswith(MessageSignal.Cancel)

    async def stream_new_messages(self):
        """流式获取新消息"""
        while True:
            message = await asyncio.to_thread(subscribe_message, self.task_id)
            if not message:
                return

            if self._should_skip_message(message):
                continue
            if self._is_terminal_message(message):
                return
            if message.startswith(MessageSignal.Error):
                raise Exception(get_message_content(message))

            yield message


async def create_update_conversation(
    message: str,
    conversation: ConversationTable | None,
    agent_code: str,
    user_id: int | str,
):
    task_id = f"message-task:{uuid4().hex}"
    # 新建会话，更新task_id
    conversation_id = None
    if conversation:
        conversation_id = conversation.id

    async with db_manager.session() as session:
        if not conversation:
            title = message[:CONVERSATION_TITLE_MAX_LENGTH]
            new_conversation = ConversationCreate(
                user_id=user_id,
                title=title,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                current_agent_code=agent_code,
                task_id=task_id,
            )
            new_conversation = await conversation_curd.create(
                db=session,
                obj_input=new_conversation,
            )
            conversation_id = new_conversation.id
        else:
            if conversation:
                await conversation_curd.update(
                    db=session,
                    db_obj=conversation,
                    obj_input={"task_id": task_id},
                )

        return conversation_id, task_id


async def save_user_message(
    *,
    message: str,
    agent_code: str,
    conversation_id: str,
    data: Dict[str, Any],
    hidden=False,
    files=List[str],
):
    # 保存用户消息
    message_pkg = MessagePackage(
        package_id=0,
        package_type=0,
        status=1,
        data=message,
    )
    async with db_manager.session() as session:
        new_message = MessageCreate(
            agent_code=agent_code,
            conversation_id=conversation_id,
            message_type=MessageType.HUMAN,
            content=[message_pkg.model_dump()],
            data_object=data,
            hidden=hidden,
            files=files,
        )
        await message_curd.create(db=session, obj_input=new_message)
