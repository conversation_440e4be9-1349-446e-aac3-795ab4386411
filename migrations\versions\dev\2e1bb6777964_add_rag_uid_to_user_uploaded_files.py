"""add rag_uid to user_uploaded_files

Revision ID: 2e1bb6777964
Revises: f1841b297ff2
Create Date: 2025-09-24 15:49:40.033532

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '2e1bb6777964'
down_revision: Union[str, None] = 'f1841b297ff2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_uploaded_files', sa.Column('rag_uid', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=""))
    op.create_index(op.f('ix_user_uploaded_files_rag_uid'), 'user_uploaded_files', ['rag_uid'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_uploaded_files_rag_uid'), table_name='user_uploaded_files')
    op.drop_column('user_uploaded_files', 'rag_uid')
    # ### end Alembic commands ###
