# 启动服务 确保 PYTHONPATH 包含项目根目录或 src 目录
# set PYTHONPATH=d:\projects\agent-server\src
# uvicorn src.core.main:app --reload --port 8000 --host 0.0.0.0

# 使用 curl 或 Postman 发送含有 Authorization 头的请求：
# curl -H "Authorization: Bearer CF1f-M0MRKFoqNJmYJyO8zjoxfQ" http://localhost:8000/admin/data
# <AUTHOR> xiangjh
# pylint: disable=wrong-import-position
import os
import sys
import uvicorn
from dotenv import load_dotenv

load_dotenv()

from agent_server.core.api.router import initialize_routers
from agent_server.core.server.app import create_app
from agent_server.core.api.router import (
    router_private,
    router_public,
)
from agent_server.gateway.main import router as gateway_router

main_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
config_path = os.path.join(main_dir, "config", "application.yml")
# 统一初始化路由
initialize_routers(config_path)

# 创建应用
app = create_app(routers=[router_private, router_public, gateway_router])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
