from agent_server.core.rag.ragflow import Rag<PERSON>lowRAGService
from agent_server.core.config.app_logger import logger

# Helper function to get RAG service instance
def get_rag_service(service_name: str = "ragflow") -> RagFlowRAGService:
    """Get RAG service instance by dynamically loading the configured module

    Args:
        service_name: Name of the RAG service to load (default: "ragflow")

    Returns:
        RagFlowRAGService: Instance of the RAG service

    Raises:
        HTTPException: If service configuration not found or module loading fails
    """
    try:
        # Import config and importlib here to avoid circular imports
        from agent_server.core.config.app_config import config
        import importlib

        # Get RAG service configuration
        if not hasattr(config, "rag_service") or not config.rag_service:
            logger.error("RAG service configuration not found in application.yml")
            raise Exception("RAG service configuration not found")

        services = config.rag_service.services
        if not services:
            logger.error("No RAG services configured in application.yml")
            raise Exception("No RAG services configured")

        # Find the requested service configuration
        service_config = None
        for service in services:
            if service.get("name") == service_name:
                service_config = service
                break

        if not service_config:
            logger.error(f"RAG service '{service_name}' not found in configuration")
            raise Exception(f"RAG service '{service_name}' not configured")

        # Get the module path from configuration
        module_path = service_config.get("module_path")
        if not module_path:
            logger.error(f"No module_path specified for RAG service '{service_name}'")
            raise Exception(f"Module path not configured for service '{service_name}'")

        # Dynamically import the module
        try:
            module = importlib.import_module(module_path)
            # logger.info(f"Successfully imported RAG service module: {module_path}")
        except ImportError as e:
            logger.error(f"Failed to import RAG service module '{module_path}': {e}")
            raise Exception(
                f"Failed to import RAG service module: {module_path}",
            )

        # Get the RagFlowRAGService class from the module
        if not hasattr(module, "RagFlowRAGService"):
            logger.error(
                f"Module '{module_path}' does not contain 'RagFlowRAGService' class"
            )
            raise Exception(
                f"RagFlowRAGService class not found in module: {module_path}"
            )

        rag_service_class = getattr(module, "RagFlowRAGService")

        # Create and return an instance of the RAG service
        service_instance = rag_service_class()
        logger.info(
            f"Successfully created RAG service instance from module: {module_path}"
        )
        return service_instance

    except Exception as e:
        logger.error(f"Unexpected error initializing RAG service '{service_name}': {e}")
        raise e
