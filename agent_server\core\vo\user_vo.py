from dataclasses import dataclass
from typing import Annotated, Set
from pydantic import Field

# 用户信息数据封装类
# <AUTHOR> xiangjh
@dataclass
class UserVO:
    userId: Annotated[str, Field(..., min_length=1, description="用户唯一标识")]
    username: str | None = Field(None, description="登录用户名")
    nickName: str | None = Field(None, description="用户昵称（可选）")
    disabled: bool | None = None
    permissions: set [str] | None = Field(None, description="权限集合")
    token: str | None = Field(None, description="用户令牌")
    
   