from datetime import datetime, timezone
from typing import Optional
from uuid import uuid4
from sqlmodel import SQLModel, Field, Column
from sqlalchemy import DateTime, CheckConstraint, Index
from enum import Enum


class FileStatus(str, Enum):
    """文件处理状态枚举"""

    UPLOADING = "UPLOADING"
    UPLOADED = "UPLOADED"
    PROCESSING = "PROCESSING"
    READY = "READY"
    ERROR = "ERROR"


class UserUploadedFilesBase(SQLModel):
    """用户上传文件基础模型"""

    id: str = Field(
        default_factory=lambda: uuid4().hex,
        primary_key=True,
        description="文件记录唯一标识符",
    )
    user_id: int = Field(index=True, nullable=False, description="上传文件的用户ID")
    name: str = Field(description="用户上传时的原始文件名")
    hash: str = Field(index=True, description="文件内容的SHA256哈希值，用于去重")
    size: int = Field(description="文件大小（字节）")
    type: str = Field(
        max_length=100, description="文件MIME类型（如 'application/pdf', 'text/plain'）"
    )
    rag_file_id: str = Field(
        index=True, description="RAG 系统返回的文档ID（关键关联字段）"
    )
    rag_uid: str = Field(
        default=None,
        index=True,
        description="RAG 服务生成的文档唯一性标识，用于区分 hash 相同但处理方式不同的文件",
    )
    rag_service: str = Field(
        default="ragflow",
        max_length=100,
        description="RAG 服务类型，用于区分不同的RAG服务提供商（如 'ragflow', 'langchain', 'llamaindex' 等）",
    )
    status: str = Field(
        max_length=20, default=FileStatus.UPLOADING, description="文件当前处理状态"
    )
    error_message: Optional[str] = Field(
        default=None, description="文件解析失败时的错误详情（仅在状态为error时填充）"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="记录创建时间戳",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="最后修改时间戳",
    )


class UserUploadedFilesTable(UserUploadedFilesBase, table=True):
    """数据库中的用户上传文件模型"""

    __tablename__ = "user_uploaded_files"

    # 添加约束：文件大小必须为正数
    __table_args__ = (
        CheckConstraint("size > 0", name="check_positive_file_size"),
        # 为性能优化添加复合索引
        Index("idx_user_files", "user_id", "status"),
        Index("idx_file_hash_user", "hash", "user_id"),
        Index("idx_ragflow_doc", "rag_file_id"),
    )


class UserUploadedFilesCreate(UserUploadedFilesBase):
    """创建用户上传文件记录的模型"""

    pass


class UserUploadedFilesRead(UserUploadedFilesBase):
    """API响应中的用户上传文件模型"""

    pass


class UserUploadedFilesUpdate(SQLModel):
    """更新用户上传文件记录的模型"""

    name: Optional[str] = None
    hash: Optional[str] = None
    size: Optional[int] = None
    type: Optional[str] = None
    rag_file_id: Optional[str] = None
    rag_service: Optional[str] = None
    status: Optional[FileStatus] = None
    error_message: Optional[str] = None
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), description="更新时间"
    )


class UserUploadedFilesInDBBase(UserUploadedFilesBase):
    """数据库中的用户上传文件基础模型"""

    pass


class UserUploadedFilesInDB(UserUploadedFilesInDBBase):
    """数据库中的完整用户上传文件模型"""

    pass
