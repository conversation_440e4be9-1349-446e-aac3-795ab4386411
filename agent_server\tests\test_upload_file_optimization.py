"""
测试文件上传优化功能

测试 upload_file 方法中的优化逻辑：
1. 检查数据库中是否已存在具有相同 rag_uid 和 hash 值的文件记录
2. 如果找到匹配的记录，跳过 RAG 服务上传
3. 如果没有找到匹配的记录，执行完整的上传流程
"""

import pytest
import hashlib
from unittest.mock import AsyncMock, MagicMock, patch
from agent_server.core.rag.rag_service import RAGServiceBase, FileUploadResult, RAGFileUploadResult
from agent_server.core.services.database.schemas.user_uploaded_files import FileStatus


class TestRAGService(RAGServiceBase):
    """用于测试的 RAG 服务实现"""
    
    def __init__(self):
        super().__init__("test", allowed_types=["text/plain"])
        self.upload_called = False
    
    def _gen_uid(self, hash: str):
        return f"test/{hash}"
    
    async def upload_file_to_rag_service(self, **kwargs):
        """模拟 RAG 服务上传"""
        self.upload_called = True
        return RAGFileUploadResult(
            success=True,
            rag_file_id="new_rag_file_id",
            status=FileStatus.UPLOADING
        )
    
    async def parse_file(self, file_id: str):
        pass
    
    async def get_file_status(self, file_id: str) -> dict:
        return {}
    
    async def delete_file_from_rag_service(self, user_id: str, file_ids: list[str]) -> bool:
        return True
    
    async def retrieve_from_documents(self, query: str, user_id: str, file_ids: list[str]) -> list[dict]:
        return []


class TestUploadFileOptimization:
    """测试文件上传优化功能"""
    
    @pytest.fixture
    def rag_service(self):
        """创建测试用的 RAG 服务实例"""
        return TestRAGService()
    
    @pytest.fixture
    def test_data(self):
        """测试数据"""
        file_content = b"test file content"
        return {
            "user_id": "test_user",
            "file_content": file_content,
            "file_name": "test.txt",
            "file_hash": hashlib.sha256(file_content).hexdigest()
        }
    
    @pytest.mark.asyncio
    async def test_upload_with_existing_file(self, rag_service, test_data):
        """测试找到现有文件记录时跳过 RAG 服务上传"""
        
        # 模拟现有文件记录
        existing_file_mock = MagicMock()
        existing_file_mock.status = FileStatus.READY
        existing_file_mock.rag_file_id = "existing_rag_file_id"
        
        with patch('agent_server.core.rag.rag_service.user_uploaded_files_crud') as mock_crud, \
             patch('agent_server.core.rag.rag_service.db_manager') as mock_db:
            
            # 模拟数据库会话
            mock_session = AsyncMock()
            mock_db.session.return_value.__aenter__.return_value = mock_session
            
            # 模拟找到现有文件
            mock_crud.get_by_rag_uid_and_hash.return_value = existing_file_mock
            
            # 模拟保存文件成功
            mock_save_result = MagicMock()
            mock_save_result.id = "new_user_file_id"
            
            # 模拟 _save_file 方法
            with patch.object(rag_service, '_save_file', return_value=mock_save_result):
                # 重置上传调用标志
                rag_service.upload_called = False
                
                # 执行上传
                result = await rag_service.upload_file(
                    user_id=test_data["user_id"],
                    file_content=test_data["file_content"],
                    file_name=test_data["file_name"]
                )
                
                # 验证结果
                assert not rag_service.upload_called, "RAG 服务不应该被调用"
                assert result.success, "上传应该成功"
                assert result.rag_file_id == "existing_rag_file_id", "应该使用现有文件的 rag_file_id"
                assert result.status == FileStatus.READY, "应该使用现有文件的状态"
    
    @pytest.mark.asyncio
    async def test_upload_without_existing_file(self, rag_service, test_data):
        """测试没有找到现有文件记录时执行完整上传流程"""
        
        with patch('agent_server.core.rag.rag_service.user_uploaded_files_crud') as mock_crud, \
             patch('agent_server.core.rag.rag_service.db_manager') as mock_db:
            
            # 模拟数据库会话
            mock_session = AsyncMock()
            mock_db.session.return_value.__aenter__.return_value = mock_session
            
            # 模拟没有找到现有文件
            mock_crud.get_by_rag_uid_and_hash.return_value = None
            
            # 模拟保存文件成功
            mock_save_result = MagicMock()
            mock_save_result.id = "new_user_file_id"
            
            # 模拟 _save_file 方法
            with patch.object(rag_service, '_save_file', return_value=mock_save_result):
                # 重置上传调用标志
                rag_service.upload_called = False
                
                # 执行上传
                result = await rag_service.upload_file(
                    user_id=test_data["user_id"],
                    file_content=test_data["file_content"],
                    file_name=test_data["file_name"]
                )
                
                # 验证结果
                assert rag_service.upload_called, "RAG 服务应该被调用"
                assert result.success, "上传应该成功"
                assert result.rag_file_id == "new_rag_file_id", "应该使用新上传文件的 rag_file_id"
                assert result.status == FileStatus.UPLOADING, "应该使用新上传文件的状态"
    
    @pytest.mark.asyncio
    async def test_database_query_error_fallback(self, rag_service, test_data):
        """测试数据库查询出错时的回退处理"""
        
        with patch('agent_server.core.rag.rag_service.user_uploaded_files_crud') as mock_crud, \
             patch('agent_server.core.rag.rag_service.db_manager') as mock_db:
            
            # 模拟数据库会话
            mock_session = AsyncMock()
            mock_db.session.return_value.__aenter__.return_value = mock_session
            
            # 模拟数据库查询出错
            mock_crud.get_by_rag_uid_and_hash.side_effect = Exception("Database error")
            
            # 模拟保存文件成功
            mock_save_result = MagicMock()
            mock_save_result.id = "new_user_file_id"
            
            # 模拟 _save_file 方法
            with patch.object(rag_service, '_save_file', return_value=mock_save_result):
                # 重置上传调用标志
                rag_service.upload_called = False
                
                # 执行上传
                result = await rag_service.upload_file(
                    user_id=test_data["user_id"],
                    file_content=test_data["file_content"],
                    file_name=test_data["file_name"]
                )
                
                # 验证结果：数据库查询出错时应该回退到完整上传流程
                assert rag_service.upload_called, "数据库查询出错时应该执行完整上传流程"
                assert result.success, "上传应该成功"
                assert result.rag_file_id == "new_rag_file_id", "应该使用新上传文件的 rag_file_id"
