from .util import create_message_chunks

MESSAGE = """
<message-embedded>
    <widget>
        <code>@BuildIn/Message</code>
        <props>
            <url>/chat/test</url>
            <body>
            {
                "message": "markdown",
                "agent_code": "default"
            }
            </body>
            <agentCode>default</agentCode>
        </props>
    </widget>
</message-embedded>


"""

def create_test_message_chunks():
    return create_message_chunks(MESSAGE, step=40)
# 