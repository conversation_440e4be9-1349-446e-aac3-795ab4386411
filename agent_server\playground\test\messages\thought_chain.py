import asyncio
from agent_server.core.message.thought_chain import <PERSON><PERSON>hainGenerator
from agent_server.core.message.transmitter import Transmitter


def thought_chain_generator():
    generator = ThoughtChainGenerator()
    yield generator.create()
    # index = 0
    yield generator.create_node(
        title="Step1", description="hello world", content="", status="pending"
    )
    yield generator.update_node_content(0, "## I am thinking...\n\n")
    yield generator.update_node_content(0, "## I am thinking...I am thinking...\n\n")
    yield generator.update_node_content(
        0, "## I am thinking...I am thinking...I am thinking...\n\n"
    )
    yield generator.finish_node(0)

    # index = 1
    yield generator.create_node(
        title="Step2", description="hello world", content="", status="pending"
    )
    yield generator.update_node_content(1, "## I am thinking...\n\n")
    yield generator.update_node_content(1, "## I am thinking...I am thinking...\n\n")
    yield generator.update_node_content(
        1, "## I am thinking...I am thinking...I am thinking...\n\n"
    )
    yield generator.finish_node(1)

    # index = 2
    yield generator.create_node(
        title="Step3", description="hello world", content="", status="pending"
    )
    yield generator.update_node_content(2, "## I am thinking...\n\n")
    yield generator.update_node_content(2, "## I am thinking...I am thinking...\n\n")
    yield generator.update_node_content(
        2, "## I am thinking...I am thinking...I am thinking...\n\n"
    )
    yield generator.finish_node(2)


async def thought_chain_event_generator(transmitter: Transmitter):
    for message in thought_chain_generator():
        yield {
            "data": message,
            "package_type": 0,
            "is_last": False,
            "is_new_package": False,
        }

    yield {"data": "", "package_type": 0, "is_last": True, "is_new_package": False}
