from .util import create_message_chunks

MESSAGE = """
工具调用

<message-embedded>
  <state>
    <set>
      <strategy>replace</strategy>
      <path>tool1Status</path>
      <value>loading</value>
    </set>
  </state>

  <widget>
    <code>@BuildIn/Tool</code>
    <props>
        <name>查询工具调用</name>
        <result>工具输出结果xxxxx</result>
        <status>{{state.tool1Status}}</status>
    </props>
  </widget>
</message-embedded>

pending...pending...pending...pending...

<message-embedded>
  <state>
    <set>
      <strategy>replace</strategy>
      <path>tool1Status</path>
      <value>success</value>
    </set>
  </state>
</message-embedded>
"""

def create_message_state_message_chunks():
    return create_message_chunks(MESSAGE, package_type=0)
