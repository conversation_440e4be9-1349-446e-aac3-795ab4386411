from .util import create_message_chunks

all_message = """


# 中文排版示例

## 本篇引语
>《学而》是《论语》第一篇的篇名。《论语》中各篇一般都是以第一章的前二三个字作为该篇的篇名。《学而》一篇包括16章，内容涉及诸多方面。其中重点是「吾日三省吾身」；「节用而爱人，使民以时」；「礼之用，和为贵」以及仁、孝、信等道德范畴。

## 原文
子曰：「学而时习之，不亦说乎？有朋自远方来，不亦乐乎？人不知，而不愠，不亦君子乎？」

## 译文
孔子说：「学了又时常温习和练习，不是很愉快吗？有志同道合的人从远方来，不是很令人高兴的吗？人家不了解我，我也不怨恨、恼怒，不也是一个有德的君子吗？」

## 评析
宋代著名学者朱熹对此章评价极高，说它是「入道之门，积德之基」。本章这三句话是人们非常熟悉的。历来的解释都是：学了以后，又时常温习和练习，不也高兴吗等等。三句话，一句一个意思，前后句子也没有什么连贯性。但也有人认为这样解释不符合原义，指出：
* 这里的「学」不是指学习，而是指学说或主张；
* 「时」不能解为时常，而是时代或社会的意思；
* 「习」不是温习，而是使用，引申为采用。

而且，这三句话不是孤立的，而是前后相互连贯的。这三句的意思是：自己的学说，要是被社会采用了，那就太高兴了；退一步说，要是没有被社会所采用，可是很多朋友赞同我的学说，纷纷到我这里来讨论问题，我也感到快乐；再退一步说，即使社会不采用，人们也不理解我，我也不怨恨，这样做，不也就是君子吗？（见《齐鲁学刊》1986年第6期文）这种解释可以自圆其说，而且也有一定的道理，供读者在理解本章内容时参考。

此外，在对「人不知，而不愠」一句的解释中，也有人认为，「人不知」的后面没有宾语，人家不知道什么呢？当时因为孔子有说话的特定环境，他不需要说出知道什么，别人就可以理解了，却给后人留下一个谜。有人说，这一句是接上一句说的，从远方来的朋友向我求教，我告诉他，他还不懂，我却不怨恨。这样，「人不知」就是「人家不知道我所讲述的」了。这样的解释似乎有些牵强。

总之，本章提出以学习为乐事，做到人不知而不愠，反映出孔子学而不厌、诲人不倦、注重修养、严格要求自己的主张。这些思想主张在《论语》书中多处可见，有助于对第一章内容的深入了解。

* If you wish to succeed, you should use persistence as your good friend, experience as your reference, prudence as your brother and hope as your sentry.  
如果你希望成功，当以恒心为良友，以经验为参谋，以谨慎为兄弟，以希望为哨兵。
* Sometimes one pays most for the things one gets for nothing.  
有时候一个人为不花钱得到的东西付出的代价最高。
* Only those who have the patience to do simple things perfectly ever acquire the skill to do difficult things easily.
只有有耐心圆满完成简单工作的人，才能够轻而易举的完成困难的事。

## 英文排版

Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#39;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.

> Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

## The standard Lorem Ipsum passage, used since the 1500s

"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."

Section 1.10.32 of "de Finibus Bonorum et Malorum", written by Cicero in 45 BC
"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

# Markdown 语法和 MWeb 使用说明

## Markdown 的设计哲学

> Markdown 的目標是實現「易讀易寫」。
> 不過最需要強調的便是它的可讀性。一份使用 Markdown 格式撰寫的文件應該可以直接以純文字發佈，並且看起來不會像是由許多標籤或是格式指令所構成。
> Markdown 的語法有個主要的目的：用來作為一種網路內容的*寫作*用語言。


<!-- more -->

## 本文约定

如果有写 `效果如下：`， 在 MWeb 编辑状态下只有用 `CMD + 4` 或 `CMD + R` 预览才可以看效果。

## 标题

Markdown 语法：

```
# 第一级标题 `<h1>` 
## 第二级标题 `<h2>` 
### 第三级标题 `<h3>` 
#### 第四级标题 `<h4>` 
##### 第五级标题 `<h5>` 
###### 第六级标题 `<h6>` 
```

效果如下：

# 第一级标题 `<h1>` 
## 第二级标题 `<h2>` 
### 第三级标题 `<h3>` 
#### 第四级标题 `<h4>` 
##### 第五级标题 `<h5>` 
###### 第六级标题 `<h6>` 


## 链接
Markdown 语法：

```
email <<EMAIL>>  
[GitHub](http://github.com)  
自动生成链接  <http://www.github.com/>  
```

`Control + Shift + L` 可插入Markdown语法。
如果是 MWeb 的文档库中的文档，拖放或 `CMD + Option + I` 导入非图片时，会生成链接。

效果如下：

Email 链接： <<EMAIL>>  
[连接标题Github网站](http://github.com)  
自动生成链接： <http://www.github.com/>  

## 多行或者一段代码

Markdown 语法：

	​```js
	function fancyAlert(arg) {
	  if(arg) {
	    $.facebox({div:&#39;#foo&#39;})
	  }
	}
	​```

`CMD + Shift + K` 可插入Markdown语法。效果如下：

```js
function fancyAlert(arg) {
	if(arg) {
		$.facebox({div:&#39;#foo&#39;})
	}
}
```


## 强调

Markdown 语法：

```
*这些文字会生成`<em>`*
_这些文字会生成`<u>`_

**这些文字会生成`<strong>`**
__这些文字会生成`<strong>`__

==这些文字会生成`<mark>`==
```

在 MWeb 中的快捷键为： `CMD + U`、`CMD + I`、`CMD + B`。效果如下：

*这些文字会生成`<em>`*  
_这些文字会生成`<u>`_

**这些文字会生成`<strong>`**  
__这些文字会生成`<strong>`__

<mark>这些文字会生成`<mark>`</mark>

## 换行

四个及以上空格加回车。
如果不想打这么多空格，只要回车就为换行，请勾选：`Preferences` - `Themes` - `Translate newlines to <br> tags`

## 列表

### 无序列表

Markdown 语法：

```
* 项目一 无序列表 `* + 空格键`
* 项目二
	* 项目二的子项目一 无序列表 `TAB + * + 空格键`
	* 项目二的子项目二
		* 子项目
			* 子项目
```

在 MWeb 中的快捷键为： `Option + U`。效果如下：

* 项目一 无序列表 `* + 空格键`
* 项目二
	* 项目二的子项目一 无序列表 `TAB + * + 空格键`
	* 项目二的子项目二
		* 子项目
			* 子项目

### 有序列表

Markdown 语法：

```
1. 项目一 有序列表 `数字 + . + 空格键`
2. 项目二 
3. 项目三
	1. 项目三的子项目一 有序列表 `TAB + 数字 + . + 空格键`
	2. 项目三的子项目二
```

效果如下：

1. 项目一 有序列表 `数字 + . + 空格键`
2. 项目二 
![GitHub set up](/assets/dynamic-page-qa-logo.png)

3. 项目三
	1. 项目三的子项目一 有序列表 `TAB + 数字 + . + 空格键`
	2. 项目三的子项目二
4. 项目四

### 列表中嵌入代码块语法

    1. 项目一 有序列表 `数字 + . + 空格键`
        
        列表中嵌入代码块必须前后空一行，如这个写法
           
        ```js
        function fancyAlert(arg) {
          if(arg) {
            $.facebox({div:&#39;#foo&#39;})
          }
        }
        ```
        
        其他文本。
        
    1. 项目二

效果如下：
1. 项目一 有序列表 `数字 + . + 空格键`
    
    列表中嵌入代码块必须前后空一行，如这个写法
       
    ```js
    function fancyAlert(arg) {
      if(arg) {
        $.facebox({div:&#39;#foo&#39;})
      }
    }
    ```
    
    其他文本。
    
2. 项目二

### 任务列表（Task lists）
Markdown 语法：

```
- [ ] 任务一 未做任务 `- + 空格 + [ ]`
- [x] 任务二 已做任务 `- + 空格 + [x]`
    - [ ] 任务三 未做任务 `- + 空格 + [ ]`
- [x] 任务四 已做任务 `- + 空格 + [x]`
```

效果如下：

- [ ] 任务一 未做任务 `- + 空格 + [ ]`
- [x] 任务二 已做任务 `- + 空格 + [x]`
    - [ ] 任务三 未做任务 `- + 空格 + [ ]`
- [x] 任务四 已做任务 `- + 空格 + [x]`

## 图片

Markdown 语法：

```
![GitHub set up](/assets/dynamic-page-qa-logo.png)
格式: ![Alt Text](url)
```

`Control + Shift + I` 可插入Markdown语法。
如果是 MWeb 的文档库中的文档，还可以用拖放图片、`CMD + V` 粘贴、`CMD + Option + I` 导入这三种方式来增加图片。
效果如下：

![GitHub set up](/assets/dynamic-page-qa-logo.png)

MWeb 引入的特别的语法来设置图片宽度，方法是在图片描述后加 `-w + 图片宽度` 即可，比如说要设置上面的图片的宽度为 140，语法如为 `![GitHub-w140](set-up-git.gif)`：

![GitHub set up-w140](/assets/dynamic-page-qa-logo.png)


## 区块引用

Markdown 语法：

```
某某说:
> 第一行引用
> 第二行引用文字
```

`CMD + Shift + B` 可插入Markdown语法。
效果如下：

某某说:
> 第一行引用
> 第二行引用文字

## 行内代码

Markdown 语法：

```
像这样即可：`<addr>` `code`
```

`CMD + K` 可插入Markdown语法。
效果如下：

像这样即可：`<addr>` `code`

## 顺序图或流程图

更多请参考：<http://bramp.github.io/js-sequence-diagrams/>, <http://adrai.github.io/flowchart.js/>

## 表格

Markdown 语法：

```txt
第一格表头 | 第二格表头
--------- | -------------
内容单元格 第一列第一格 | 内容单元格第二列第一格
内容单元格 第一列第二格 多加文字 | 内容单元格第二列第二格
```

效果如下：

第一格表头 | 第二格表头
--------- | -------------
内容单元格 第一列第一格 | 内容单元格第二列第一格
内容单元格 第一列第二格 多加文字 | 内容单元格第二列第二格

## 删除线

Markdown 语法：

	加删除线像这样用： ~~删除这些~~

效果如下：

加删除线像这样用： ~~删除这些~~

## 分隔线

以下三种方式都可以生成分隔线：

```text
***
	
*****
	
- - -
```

效果如下：

***

*****

- - -



## Latex 公式

Markdown 语法：

块级公式：
```text

    ```math
      x = \dfrac{-b \pm \sqrt{b^2 - 4ac}}{2a} 
    ```

  $$
  f\left(
    \left[ 
      \frac{
        1+\left\{x,y\right\}
      }{
        \left(
            \frac xy + \frac yx
        \right)
        (u+1)
      }+a
    \right]^{3/2}
  \right)
  \tag {行标}
  $$
```

效果如下：
```math
x = \dfrac{-b \pm \sqrt{b^2 - 4ac}}{2a}
```

$$
f\left(
   \left[ 
     \\frac{
       1+\left\{x,y\\right\}
     }{
       \left(
          \\frac xy + \\frac yx
       \\right)
       (u+1)
     }+a
   \\right]^{3/2}
\\right)
\\tag {行标}
$$

  
行内公式：
```text
  $ J_\alpha(x) = \sum_{m=0}^\infty \frac{(-1)^m}{m! \Gamma (m + \alpha + 1)} {\left({ \frac{x}{2} }\right)}^{2m + \alpha} \text {，行内公式示例} $
```

效果如下：

$ J_\\alpha(x) = \\sum_{m=0}^\infty \\frac{(-1)^m}{m! \\Gamma (m + \\alpha + 1)} {\\left({ \\frac{x}{2} }\\right)}^{2m + \\alpha} \\text {，行内公式示例} $



## 脚注（Footnote）

Markdown 语法：

```text
这是一个脚注：[^sample_footnote]
这是另一个脚注：[^sample_footnote2]
```

效果如下：

这是一个脚注：[^sample_footnote]
这是另一个脚注：[^sample_footnote2]

[^sample_footnote]: 这里是脚注信息
[^sample_footnote2]: 这里是脚注信息


## 注释和阅读更多

<!-- comment -->
<!-- more -->

Actions->Insert Read More Comment *或者* `Command + .`
**注** 阅读更多的功能只用在生成网站或博客时，插入时注意要后空一行。

"""

thinking_message_chunks = [
    {
        "data": "I am",
        "is_last": False,
        "package_type": 2,
        "is_new_package": True,
    },
    {
        "data": "thinking...",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "thinking......",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "thinking End",
        "is_last": True,
        "package_type": 2,
        "is_new_package": False,
    },
]


def create_markdown_message_chunks():
    chunks = thinking_message_chunks + create_message_chunks(all_message, step=20)

    return chunks
