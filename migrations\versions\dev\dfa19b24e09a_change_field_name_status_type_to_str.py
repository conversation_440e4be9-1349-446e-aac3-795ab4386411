"""change field name status type to str

Revision ID: dfa19b24e09a
Revises: f8329b9ad1f3
Create Date: 2025-09-17 11:19:56.635994

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'dfa19b24e09a'
down_revision: Union[str, None] = 'f8329b9ad1f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_uploaded_files', 'status',
               existing_type=postgresql.ENUM('UPLOADING', 'PARSING', 'READY', 'ERROR', name='filestatus'),
               type_=sqlmodel.sql.sqltypes.AutoString(length=20),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_uploaded_files', 'status',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=20),
               type_=postgresql.ENUM('UPLOADING', 'PARSING', 'READY', 'ERROR', name='filestatus'),
               existing_nullable=False)
    # ### end Alembic commands ###
