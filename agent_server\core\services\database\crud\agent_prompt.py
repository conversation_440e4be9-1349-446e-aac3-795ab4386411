from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from datetime import datetime, timezone

from agent_server.core.services.database.crud.base import CRUDBase
from agent_server.core.services.database.schemas.agent_prompt import (
    AgentPromptTable,
    AgentPromptCreate,
    AgentPromptUpdate,
)
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.base import DatabaseManager

# Agent Prompt CRUD 操作实现
# <AUTHOR> xiangjh


class CRUDAgentPrompt(CRUDBase[AgentPromptTable, AgentPromptCreate, AgentPromptUpdate]):
    """Agent Prompt CRUD 操作实现"""

    async def get_by_agent_and_node(
        self, *, agent_code: str, node_code: str, skip: int = 0, limit: int = 100
    ) -> List[AgentPromptTable]:
        async with db_manager.session() as db:
            query = (
                select(AgentPromptTable)
                .where(
                    AgentPromptTable.agent_code == agent_code,
                    AgentPromptTable.node_code == node_code,
                )
                .order_by(AgentPromptTable.id.asc())
                .offset(skip)
                .limit(limit)
            )
            result = await db.execute(query)
            return result.scalars().all()

    async def get_by_agent_and_node_and_type(
        self, *, agent_code: str, p_type: str, node_code: str
    ) -> List[AgentPromptTable]:
        async with db_manager.session() as db:
            query = (
                select(AgentPromptTable)
                .where(
                    AgentPromptTable.agent_code == agent_code,
                    AgentPromptTable.node_code == node_code,
                    AgentPromptTable.p_type == p_type,
                )
                .order_by(AgentPromptTable.id.asc())
            )
            result = await db.execute(query)
            return result.scalars().all()

    def get_by_agent_and_node_and_type_sync(
        self, *, agent_code: str, node_code: str, p_type: str
    ) -> List[AgentPromptTable]:
        """同步获取指定 agent_code + node_code + p_type 的提示词列表
        供同步调用场景使用，避免在同步函数中直接 await 协程方法。
        """
        ScopedSession = DatabaseManager.createScopedSession()
        session = ScopedSession()
        try:
            result = session.execute(
                select(AgentPromptTable)
                .where(
                    AgentPromptTable.agent_code == agent_code,
                    AgentPromptTable.node_code == node_code,
                    AgentPromptTable.p_type == p_type,
                )
                .order_by(AgentPromptTable.id.asc())
            )
            return result.scalars().all()
        finally:
            ScopedSession.remove()

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: AgentPromptTable,
        obj_in: AgentPromptUpdate,
    ) -> AgentPromptTable:
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["update_time"] = datetime.now(timezone.utc)

        for field, value in obj_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


agent_prompt_crud = CRUDAgentPrompt(AgentPromptTable)
